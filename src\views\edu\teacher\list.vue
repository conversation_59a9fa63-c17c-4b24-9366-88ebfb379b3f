<template>
  <div class="app-container" />
</template>

<script>
import teacher from '@/api/edu/teacher'

export default {
  data() {
    return {
      list: null,
      page: 1,
      limit: 10,
      total: 0,
      teacherQuery: {}
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      teacher.getTeacherList(this.page, this.limit, this.teacherQuery)
        .then(response => {
          this.list = response.data.rows
          this.total = response.data.total
        }).catch(error => {
          console.log(error)
        })
    }
  }
}

</script>

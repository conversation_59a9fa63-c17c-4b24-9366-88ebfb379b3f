<template>
  <div class="app-container">
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="数据加载中"
      border
      fit
      highlight-current-row>

      <el-table-column 
      align="center" label="序号" width="70">
        <template slot-scope="scope">
          {{ (page - 1) * limit + scope.$index + 1 }}
        </template>
      </el-table-column>

      
      <el-table-column label="名称" width="110" align="center" prop="name">
        <template slot-scope="scope">
          {{ scope.row.name }}
        </template>
      </el-table-column>

      <el-table-column label="头衔" width="110" align="center" prop="level">
        <template slot-scope="scope">
          {{ scope.row.level===1?'高级讲师':'首席讲师' }}
        </template>
      </el-table-column>

      <el-table-column label="资历" width="110" align="center" prop="career">
      
      <el-table-column label="讲师简介">
        <template slot-scope="scope">
          {{ scope.row.intro }}
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import teacher from '@/api/edu/teacher'

export default {
  data() {
    return {
      list: null,
      page: 1,
      limit: 10,
      total: 0,
      teacherQuery: {}
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      teacher.getTeacherList(this.page, this.limit, this.teacherQuery)
        .then(response => {
          this.list = response.data.rows
          this.total = response.data.total
        }).catch(error => {
          console.log(error)
        })
    }
  }
}

</script>
